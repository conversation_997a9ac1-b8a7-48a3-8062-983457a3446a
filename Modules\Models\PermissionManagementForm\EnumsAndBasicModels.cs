using System;
using System.ComponentModel.DataAnnotations;

namespace ProManage.Modules.Models.PermissionManagementForm
{
    #region Enums

    /// <summary>
    /// Enumeration for permission types
    /// </summary>
    public enum PermissionType
    {
        Read = 1,
        New = 2,
        Edit = 3,
        Delete = 4,
        Print = 5
    }

    /// <summary>
    /// Enumeration for permission sources
    /// </summary>
    public enum PermissionSource
    {
        Role = 1,
        UserOverride = 2,
        Global = 3
    }

    /// <summary>
    /// Enumeration for global permission types
    /// </summary>
    public enum GlobalPermissionType
    {
        CanCreateUsers = 1,
        CanEditUsers = 2,
        CanDeleteUsers = 3,
        CanPrintUsers = 4
    }

    #endregion

    #region Basic Models

    /// <summary>
    /// Basic role model
    /// </summary>
    public class Role
    {
        public int RoleId { get; set; }
        public string RoleName { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }

    /// <summary>
    /// Extended role model for management operations
    /// </summary>
    public class RoleModel
    {
        public int RoleId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string RoleName { get; set; }
        
        [StringLength(500)]
        public string Description { get; set; }
        
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }

    /// <summary>
    /// User information model
    /// </summary>
    public class UserInfo
    {
        public int UserId { get; set; }
        public string Username { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public int? RoleId { get; set; }
        public string RoleName { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// User permission model
    /// </summary>
    public class UserPermission
    {
        public int UserPermissionId { get; set; }
        public int UserId { get; set; }
        public string FormName { get; set; }
        public bool? ReadPermission { get; set; }
        public bool? NewPermission { get; set; }
        public bool? EditPermission { get; set; }
        public bool? DeletePermission { get; set; }
        public bool? PrintPermission { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    /// <summary>
    /// Role permission model
    /// </summary>
    public class RolePermission
    {
        public int RolePermissionId { get; set; }
        public int RoleId { get; set; }
        public string FormName { get; set; }
        public bool ReadPermission { get; set; }
        public bool NewPermission { get; set; }
        public bool EditPermission { get; set; }
        public bool DeletePermission { get; set; }
        public bool PrintPermission { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    /// <summary>
    /// Global permission model
    /// </summary>
    public class GlobalPermission
    {
        public int GlobalPermissionId { get; set; }
        public int UserId { get; set; }
        public bool CanCreateUsers { get; set; }
        public bool CanEditUsers { get; set; }
        public bool CanDeleteUsers { get; set; }
        public bool CanPrintUsers { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }

    /// <summary>
    /// Effective permission model (calculated from role + user overrides)
    /// </summary>
    public class EffectivePermission
    {
        public string FormName { get; set; }
        public bool CanRead { get; set; }
        public bool CanCreate { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public bool CanPrint { get; set; }
        public PermissionSource Source { get; set; }
    }

    /// <summary>
    /// User with permissions model
    /// </summary>
    public class UserWithPermissions
    {
        public UserInfo User { get; set; }
        public List<EffectivePermission> Permissions { get; set; }
        public GlobalPermission GlobalPermissions { get; set; }
        
        public UserWithPermissions()
        {
            Permissions = new List<EffectivePermission>();
        }
    }

    /// <summary>
    /// Permission request model
    /// </summary>
    public class PermissionRequest
    {
        public int UserId { get; set; }
        public string FormName { get; set; }
        public PermissionType PermissionType { get; set; }
    }

    #endregion
}
