using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ProManage.Modules.Models.PermissionManagementForm
{
    /// <summary>
    /// Form configuration model
    /// </summary>
    public class FormConfiguration
    {
        /// <summary>
        /// Unique form name/identifier
        /// </summary>
        [Required]
        public string FormName { get; set; }

        /// <summary>
        /// Display name for the form
        /// </summary>
        [Required]
        public string DisplayName { get; set; }

        /// <summary>
        /// Category this form belongs to
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Description of the form's purpose
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Whether this form is active/available
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Order for display purposes
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Whether this form requires special permissions
        /// </summary>
        public bool RequiresSpecialPermissions { get; set; }

        /// <summary>
        /// Default permissions for new roles
        /// </summary>
        public FormPermissionSet DefaultPermissions { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public FormConfiguration()
        {
            IsActive = true;
            DisplayOrder = 0;
            RequiresSpecialPermissions = false;
            DefaultPermissions = new FormPermissionSet();
        }
    }

    /// <summary>
    /// Category configuration model
    /// </summary>
    public class CategoryConfiguration
    {
        /// <summary>
        /// Category name/identifier
        /// </summary>
        [Required]
        public string CategoryName { get; set; }

        /// <summary>
        /// Display name for the category
        /// </summary>
        [Required]
        public string DisplayName { get; set; }

        /// <summary>
        /// Description of the category
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Whether this category is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Order for display purposes
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Icon or image for the category
        /// </summary>
        public string Icon { get; set; }

        /// <summary>
        /// Forms in this category
        /// </summary>
        public List<FormConfiguration> Forms { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public CategoryConfiguration()
        {
            IsActive = true;
            DisplayOrder = 0;
            Forms = new List<FormConfiguration>();
        }
    }

    /// <summary>
    /// Collection of form configurations
    /// </summary>
    public class FormsConfigurationCollection
    {
        /// <summary>
        /// All categories with their forms
        /// </summary>
        public List<CategoryConfiguration> Categories { get; set; }

        /// <summary>
        /// All forms (flattened list)
        /// </summary>
        public List<FormConfiguration> AllForms { get; set; }

        /// <summary>
        /// Last updated timestamp
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Version of the configuration
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public FormsConfigurationCollection()
        {
            Categories = new List<CategoryConfiguration>();
            AllForms = new List<FormConfiguration>();
            LastUpdated = DateTime.Now;
            Version = "1.0";
        }

        /// <summary>
        /// Get form by name
        /// </summary>
        /// <param name="formName">Form name to find</param>
        /// <returns>Form configuration or null if not found</returns>
        public FormConfiguration GetForm(string formName)
        {
            return AllForms.FirstOrDefault(f => f.FormName.Equals(formName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Get category by name
        /// </summary>
        /// <param name="categoryName">Category name to find</param>
        /// <returns>Category configuration or null if not found</returns>
        public CategoryConfiguration GetCategory(string categoryName)
        {
            return Categories.FirstOrDefault(c => c.CategoryName.Equals(categoryName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Get forms by category
        /// </summary>
        /// <param name="categoryName">Category name</param>
        /// <returns>List of forms in the category</returns>
        public List<FormConfiguration> GetFormsByCategory(string categoryName)
        {
            var category = GetCategory(categoryName);
            return category?.Forms ?? new List<FormConfiguration>();
        }

        /// <summary>
        /// Add form to collection
        /// </summary>
        /// <param name="form">Form to add</param>
        /// <param name="categoryName">Category to add to</param>
        public void AddForm(FormConfiguration form, string categoryName = null)
        {
            if (form == null) return;

            // Add to all forms list
            if (!AllForms.Any(f => f.FormName.Equals(form.FormName, StringComparison.OrdinalIgnoreCase)))
            {
                AllForms.Add(form);
            }

            // Add to category if specified
            if (!string.IsNullOrEmpty(categoryName))
            {
                var category = GetCategory(categoryName);
                if (category != null && !category.Forms.Any(f => f.FormName.Equals(form.FormName, StringComparison.OrdinalIgnoreCase)))
                {
                    form.Category = categoryName;
                    category.Forms.Add(form);
                }
            }

            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// Remove form from collection
        /// </summary>
        /// <param name="formName">Form name to remove</param>
        public void RemoveForm(string formName)
        {
            // Remove from all forms
            AllForms.RemoveAll(f => f.FormName.Equals(formName, StringComparison.OrdinalIgnoreCase));

            // Remove from categories
            foreach (var category in Categories)
            {
                category.Forms.RemoveAll(f => f.FormName.Equals(formName, StringComparison.OrdinalIgnoreCase));
            }

            LastUpdated = DateTime.Now;
        }
    }
}
